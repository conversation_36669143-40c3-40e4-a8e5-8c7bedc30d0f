@echo off
title Health AI Assistant - Frontend
color 0B

echo.
echo 🌐 Health AI Assistant - Frontend Server
echo =======================================
echo.

if not exist "frontend" (
    echo ❌ ERROR: frontend folder not found
    echo Please run this from the health-ai project folder
    pause
    exit /b 1
)

echo 📦 Installing frontend dependencies...
cd frontend
call npm install

echo.
echo 🚀 Starting frontend development server...
echo.
echo 📍 Frontend will be available at: http://localhost:3000
echo.
echo 🔥 LEAVE THIS WINDOW OPEN - Frontend is running here
echo.
echo Starting in 3 seconds...
timeout /t 3 /nobreak >nul

call npm run dev

echo.
echo 🛑 Frontend stopped
pause
