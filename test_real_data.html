<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Your Real Health Data</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0ea5e9;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background-color: #0ea5e9;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        button:hover {
            background-color: #0284c7;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background-color: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .info {
            background-color: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 Add Your Real Health Data</h1>
        
        <div class="info">
            <strong>📝 Instructions:</strong><br>
            1. Make sure your backend is running (FINAL_WORKING_START.bat)<br>
            2. Fill in your ACTUAL health data below<br>
            3. This will replace the fake demo data with YOUR real data<br>
            4. Leave fields empty if you don't want to update them
        </div>

        <form id="healthForm">
            <div class="form-group">
                <label for="steps">Daily Steps (today):</label>
                <input type="number" id="steps" name="steps" placeholder="e.g., 8500" min="0" max="50000">
            </div>

            <div class="form-group">
                <label for="sleep_hours">Sleep Hours (last night):</label>
                <input type="number" id="sleep_hours" name="sleep_hours" placeholder="e.g., 7.5" min="0" max="24" step="0.1">
            </div>

            <div class="form-group">
                <label for="weight_kg">Weight (kg):</label>
                <input type="number" id="weight_kg" name="weight_kg" placeholder="e.g., 70.5" min="20" max="300" step="0.1">
            </div>

            <div class="form-group">
                <label for="stress_level">Stress Level (1-10, where 10 is very stressed):</label>
                <input type="number" id="stress_level" name="stress_level" placeholder="e.g., 4" min="1" max="10">
            </div>

            <div class="form-group">
                <label for="calories_consumed">Calories Consumed (today):</label>
                <input type="number" id="calories_consumed" name="calories_consumed" placeholder="e.g., 2000" min="0" max="10000">
            </div>

            <div class="form-group">
                <label for="exercise_minutes">Exercise Minutes (today):</label>
                <input type="number" id="exercise_minutes" name="exercise_minutes" placeholder="e.g., 45" min="0" max="600">
            </div>

            <button type="submit">💾 Save My Real Health Data</button>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        document.getElementById('healthForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'none';
            
            // Get form data
            const formData = new FormData(e.target);
            const data = {};
            
            // Only include non-empty fields
            for (let [key, value] of formData.entries()) {
                if (value.trim() !== '') {
                    data[key] = parseFloat(value) || value;
                }
            }
            
            if (Object.keys(data).length === 0) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ Please fill in at least one field!';
                resultDiv.style.display = 'block';
                return;
            }
            
            try {
                console.log('Sending data:', data);
                
                const response = await fetch('http://localhost:8000/api/v1/health/data/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                console.log('Response:', result);
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        ✅ <strong>Success!</strong> Your real health data has been saved!<br>
                        <br>
                        <strong>Updated data:</strong><br>
                        ${Object.entries(data).map(([key, value]) => `• ${key.replace('_', ' ')}: ${value}`).join('<br>')}
                        <br><br>
                        🎉 Now go to your Health AI dashboard to see YOUR real data!
                    `;
                    
                    // Clear form
                    e.target.reset();
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Error: ${result.detail || 'Failed to save data'}`;
                }
            } catch (error) {
                console.error('Error:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Error: Could not connect to backend. Make sure it's running on http://localhost:8000`;
            }
            
            resultDiv.style.display = 'block';
        });
    </script>
</body>
</html>
