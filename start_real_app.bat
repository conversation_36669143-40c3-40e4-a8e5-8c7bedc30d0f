@echo off
echo 🏥 Health AI Assistant - Real Production Version
echo ===============================================
echo This starts the REAL backend with MongoDB (not demo data)
echo.

REM Check if we're in the right directory
if not exist "backend" (
    echo ❌ Please run this from the health-ai root directory
    pause
    exit /b 1
)

if not exist "frontend" (
    echo ❌ Please run this from the health-ai root directory  
    pause
    exit /b 1
)

echo 📦 Installing backend dependencies...
cd backend
python -m pip install motor beanie fastapi uvicorn pydantic pydantic-settings python-dotenv loguru passlib[bcrypt] python-multipart

echo 🧪 Testing MongoDB connection...
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
mongodb_url = os.getenv('MONGODB_URL')
if mongodb_url and 'mongodb' in mongodb_url:
    print('✅ MongoDB URL configured')
    print(f'Database: {mongodb_url.split(\"/\")[-1].split(\"?\")[0] if \"/\" in mongodb_url else \"health_ai\"}')
else:
    print('❌ MongoDB URL not properly configured')
    print('Please check your .env file')
"

echo.
echo 🚀 Starting REAL backend server...
echo ⚠️  This connects to your MongoDB Atlas database
echo 📍 Backend: http://localhost:8000
echo 📚 API Docs: http://localhost:8000/docs
echo.

start "Health AI - Real Backend" cmd /k "python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

echo ⏳ Waiting for backend to start...
timeout /t 5 /nobreak >nul

echo.
echo 🌐 Starting frontend...
cd ..\frontend
start "Health AI - Frontend" cmd /k "npm run dev"

echo.
echo ✅ Both services are starting!
echo.
echo 🌐 Open your browser and go to: http://localhost:3000
echo.
echo 👤 To test with real data:
echo    1. Register a new account (this creates real data in MongoDB)
echo    2. Or login with existing account if you have one
echo.
echo ⚠️  Note: This is NOT demo data - this is real data stored in MongoDB!
echo.
pause
