@echo off
echo 🔧 Fixing MongoDB Setup for Real Data
echo =====================================

cd backend

echo 📦 Installing all required packages...
python -m pip install motor beanie fastapi uvicorn pydantic pydantic-settings python-dotenv loguru passlib[bcrypt]

echo 🧪 Testing MongoDB connection...
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
print('Environment loaded')
mongodb_url = os.getenv('MONGODB_URL')
if mongodb_url:
    print('✅ MongoDB URL found in .env')
    print(f'URL: {mongodb_url[:50]}...')
else:
    print('❌ MongoDB URL not found in .env')
"

echo.
echo 🚀 Now you need to:
echo 1. Stop the current backend (Ctrl+C if running)
echo 2. Start the real backend with: python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
echo 3. Register a new account at http://localhost:3000
echo.
echo The real backend will create data in MongoDB when you register!
echo.
pause
