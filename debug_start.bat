@echo off
echo 🔧 Debug Version - Health AI Assistant
echo =====================================
echo This version will show errors and not close automatically
echo.

REM Show current directory
echo 📍 Current directory: %CD%
echo.

REM Check if we're in the right directory
echo 📂 Checking directories...
if exist "backend" (
    echo ✅ backend directory found
) else (
    echo ❌ backend directory NOT found
    echo Please navigate to the health-ai folder first
    echo Current location: %CD%
    pause
    exit /b 1
)

if exist "frontend" (
    echo ✅ frontend directory found
) else (
    echo ❌ frontend directory NOT found
    echo Please navigate to the health-ai folder first
    pause
    exit /b 1
)

echo.
echo 🐍 Checking Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python not found or not in PATH
    echo Please install Python or add it to PATH
    pause
    exit /b 1
) else (
    echo ✅ Python is available
)

echo.
echo 📦 Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js not found or not in PATH
    echo Please install Node.js or add it to PATH
    pause
    exit /b 1
) else (
    echo ✅ Node.js is available
)

echo.
echo 📁 Checking backend files...
if exist "backend\app\main.py" (
    echo ✅ backend\app\main.py found
) else (
    echo ❌ backend\app\main.py NOT found
    echo The backend structure might be incorrect
    pause
    exit /b 1
)

if exist "backend\.env" (
    echo ✅ backend\.env found
) else (
    echo ❌ backend\.env NOT found
    echo Please create the .env file with MongoDB configuration
    pause
    exit /b 1
)

echo.
echo 📦 Installing backend dependencies...
cd backend
echo Current directory: %CD%

python -m pip install motor
if %errorlevel% neq 0 (
    echo ❌ Failed to install motor
    pause
    exit /b 1
)

python -m pip install beanie
if %errorlevel% neq 0 (
    echo ❌ Failed to install beanie
    pause
    exit /b 1
)

python -m pip install fastapi
if %errorlevel% neq 0 (
    echo ❌ Failed to install fastapi
    pause
    exit /b 1
)

python -m pip install uvicorn
if %errorlevel% neq 0 (
    echo ❌ Failed to install uvicorn
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully

echo.
echo 🧪 Testing Python imports...
python -c "import motor; print('✅ motor imported')"
python -c "import beanie; print('✅ beanie imported')"
python -c "import fastapi; print('✅ fastapi imported')"
python -c "import uvicorn; print('✅ uvicorn imported')"

echo.
echo 🔧 Testing environment file...
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
mongodb_url = os.getenv('MONGODB_URL')
if mongodb_url:
    print('✅ MongoDB URL found')
    print(f'URL starts with: {mongodb_url[:30]}...')
else:
    print('❌ MongoDB URL not found in .env')
"

echo.
echo 🚀 Attempting to start backend...
echo If this fails, you'll see the error message:
echo.

python -m uvicorn app.main:app --host 0.0.0.0 --port 8000

echo.
echo 🛑 Backend stopped or failed to start
echo Check the error messages above
pause
