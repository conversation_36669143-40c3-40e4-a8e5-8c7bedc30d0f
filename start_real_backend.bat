@echo off
echo 🏥 Starting Real Health AI Backend with MongoDB...
echo ================================================

cd backend

echo 📦 Installing required packages...
python -m pip install motor beanie fastapi uvicorn pydantic pydantic-settings python-dotenv loguru

echo 🔧 Testing MongoDB connection...
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
mongodb_url = os.getenv('MONGODB_URL', 'Not found')
print(f'MongoDB URL configured: {mongodb_url[:50]}...')
"

echo 🚀 Starting real backend server...
echo.
echo Backend will be available at: http://localhost:8000
echo API Documentation: http://localhost:8000/docs
echo.
echo This uses your real MongoDB database, not demo data!
echo.
echo Press Ctrl+C to stop the server
echo.

python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

pause
