"""
Super Simple Working Backend - Guaranteed to Work
This version will start even if MongoDB is not working
"""

from fastapi import FastAP<PERSON>, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any
import json
import os
from datetime import datetime
import secrets
import hashlib

# Simple in-memory storage (will be replaced with MongoDB when working)
users_db = {}
health_data_db = {}

# Create FastAPI app
app = FastAPI(
    title="Health AI Assistant - Simple Working Version",
    version="1.0.0",
    description="This version works without MongoDB for testing"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Models
class UserCreate(BaseModel):
    email: str
    password: str
    first_name: str
    last_name: str

class UserLogin(BaseModel):
    username: str
    password: str

# Helper functions
def hash_password(password: str) -> str:
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password: str, hashed: str) -> bool:
    return hash_password(password) == hashed

# Routes
@app.get("/")
async def root():
    return {
        "message": "Health AI Assistant API - Simple Working Version",
        "version": "1.0.0",
        "status": "running",
        "users_count": len(users_db),
        "timestamp": datetime.utcnow().isoformat()
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "health-ai-backend",
        "version": "1.0.0",
        "database": "in-memory",
        "users": len(users_db)
    }

@app.post("/api/v1/auth/register")
async def register(user_data: UserCreate):
    """Register a new user"""
    
    print(f"📝 Registration attempt: {user_data.email}")
    
    # Check if user already exists
    if user_data.email in users_db:
        print(f"❌ Email already exists: {user_data.email}")
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Create user
    user_id = f"user-{len(users_db) + 1}"
    hashed_password = hash_password(user_data.password)
    
    user = {
        "id": user_id,
        "email": user_data.email,
        "password": hashed_password,
        "first_name": user_data.first_name,
        "last_name": user_data.last_name,
        "is_active": True,
        "is_verified": True,
        "created_at": datetime.utcnow().isoformat(),
        "profile": {
            "age": None,
            "gender": None,
            "health_goals": ["general_fitness"],
            "activity_level": "moderately_active"
        },
        "privacy_settings": {
            "federated_learning_participation": True,
            "allow_data_analytics": True,
            "share_data_for_research": False,
            "data_retention_days": 365
        }
    }
    
    users_db[user_data.email] = user
    
    print(f"✅ User registered successfully: {user_data.email}")
    print(f"📊 Total users: {len(users_db)}")
    
    return {
        "message": "Registration successful",
        "user": {
            "id": user_id,
            "email": user_data.email,
            "first_name": user_data.first_name,
            "last_name": user_data.last_name,
            "is_active": True,
            "created_at": user["created_at"]
        }
    }

@app.post("/api/v1/auth/login")
async def login(username: str = Form(...), password: str = Form(...)):
    """Login user"""
    
    print(f"🔐 Login attempt: {username}")
    
    # Check if user exists
    user = users_db.get(username)
    if not user:
        print(f"❌ User not found: {username}")
        raise HTTPException(status_code=401, detail="Invalid email or password")
    
    # Check password
    if not verify_password(password, user["password"]):
        print(f"❌ Invalid password for: {username}")
        raise HTTPException(status_code=401, detail="Invalid email or password")
    
    print(f"✅ Login successful: {username}")
    
    # Create token (simplified)
    access_token = f"token-{secrets.token_hex(16)}"
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "id": user["id"],
            "email": user["email"],
            "first_name": user["first_name"],
            "last_name": user["last_name"],
            "profile": user["profile"],
            "privacy_settings": user["privacy_settings"],
            "is_active": user["is_active"],
            "is_verified": user["is_verified"],
            "created_at": user["created_at"]
        }
    }

@app.get("/api/v1/auth/me")
async def get_current_user():
    """Get current user (simplified)"""
    # For demo, return the first user or a default user
    if users_db:
        first_user = list(users_db.values())[0]
        return {
            "id": first_user["id"],
            "email": first_user["email"],
            "first_name": first_user["first_name"],
            "last_name": first_user["last_name"],
            "profile": first_user["profile"],
            "privacy_settings": first_user["privacy_settings"],
            "is_active": first_user["is_active"],
            "is_verified": first_user["is_verified"],
            "created_at": first_user["created_at"]
        }
    else:
        # Default demo user
        return {
            "id": "demo-user",
            "email": "<EMAIL>",
            "first_name": "Demo",
            "last_name": "User",
            "profile": {
                "age": 30,
                "gender": "female",
                "health_goals": ["general_fitness"],
                "activity_level": "moderately_active"
            },
            "privacy_settings": {
                "federated_learning_participation": True,
                "allow_data_analytics": True,
                "share_data_for_research": False,
                "data_retention_days": 365
            },
            "is_active": True,
            "is_verified": True,
            "created_at": datetime.utcnow().isoformat()
        }

@app.get("/api/v1/dashboard/metrics")
async def get_dashboard_metrics():
    """Get dashboard metrics"""
    return {
        "health_score": 75,
        "recent_activity": {
            "steps": 8500,
            "calories_burned": 320,
            "active_minutes": 45,
            "distance_km": 6.8
        },
        "sleep_quality": 80,
        "stress_level": 4,
        "recommendations_count": 5,
        "goals_progress": [
            {
                "goal": "Daily Steps",
                "progress": 8500,
                "target": 10000,
                "unit": "steps",
                "status": "on_track"
            }
        ],
        "health_trends": {
            "steps": {"trend": "increasing"},
            "sleep": {"trend": "stable"},
            "stress": {"trend": "decreasing"}
        },
        "last_updated": datetime.utcnow().isoformat()
    }

@app.get("/api/v1/health/data")
async def get_health_data():
    """Get health data"""
    # Return sample health data
    sample_data = []
    for i in range(7):  # Last 7 days
        date = datetime.utcnow().replace(day=datetime.utcnow().day - i)
        sample_data.append({
            "id": f"data-{i}",
            "date": date.isoformat(),
            "data_source": "simulated",
            "vital_signs": {"heart_rate_bpm": 70 + i},
            "activity": {"steps": 8000 + i * 500},
            "sleep": {"total_sleep_hours": 7.5 + (i % 2) * 0.5},
            "nutrition": {"calories_consumed": 2000 + i * 100},
            "mental_health": {"stress_level": 3 + (i % 3)},
            "weight_kg": 70.0
        })
    
    return sample_data

@app.post("/api/v1/chat/message")
async def send_chat_message(content: str = Form(...), category: str = Form("general")):
    """Send a message to AI assistant"""
    
    responses = {
        "nutrition": "Great question about nutrition! I recommend focusing on a balanced diet with plenty of vegetables, lean proteins, and whole grains.",
        "fitness": "For your fitness goals, I suggest starting with 30 minutes of moderate exercise daily. Consistency is key!",
        "sleep": "Good sleep is crucial for health! Aim for 7-9 hours nightly with a consistent bedtime routine.",
        "mental_health": "Mental wellness is important. Consider stress-reduction techniques like meditation or deep breathing.",
        "general": "I'm here to help with your health journey! What specific area would you like to focus on?"
    }
    
    response_content = responses.get(category, responses["general"])
    
    return {
        "id": f"msg-{secrets.token_hex(8)}",
        "session_id": "session-1",
        "message_type": "assistant",
        "content": response_content,
        "category": category,
        "recommendations": [
            "Track your daily water intake",
            "Aim for 10,000 steps per day",
            "Maintain a consistent sleep schedule"
        ],
        "confidence_score": 0.85,
        "timestamp": datetime.utcnow().isoformat()
    }

@app.get("/api/v1/chat/sessions")
async def get_chat_sessions():
    """Get chat sessions"""
    return [
        {
            "id": "session-1",
            "title": "Health Chat",
            "description": "General health discussion",
            "is_active": True,
            "message_count": 2,
            "last_activity": datetime.utcnow().isoformat()
        }
    ]

@app.get("/api/v1/chat/sessions/{session_id}/messages")
async def get_chat_messages(session_id: str):
    """Get messages for a chat session"""
    return [
        {
            "id": "msg-1",
            "session_id": session_id,
            "message_type": "user",
            "content": "Hello, I'd like some health advice",
            "timestamp": datetime.utcnow().isoformat()
        },
        {
            "id": "msg-2",
            "session_id": session_id,
            "message_type": "assistant",
            "content": "Hello! I'm here to help with your health journey. What would you like to know?",
            "recommendations": ["Stay hydrated", "Get regular exercise", "Maintain good sleep habits"],
            "timestamp": datetime.utcnow().isoformat()
        }
    ]

if __name__ == "__main__":
    import uvicorn
    print("🏥 Starting Health AI Assistant - Simple Working Backend")
    print("=" * 60)
    print("📍 Backend: http://localhost:8000")
    print("📚 API Docs: http://localhost:8000/docs")
    print("🔧 This version works without MongoDB!")
    print("✅ Real user registration supported")
    print("=" * 60)
    uvicorn.run(app, host="0.0.0.0", port=8000)
