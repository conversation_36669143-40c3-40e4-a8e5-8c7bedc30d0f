@echo off
title Health AI Assistant - Ultimate Fix
color 0A

echo.
echo  ██╗  ██╗███████╗ █████╗ ██╗  ████████╗██╗  ██╗     █████╗ ██╗
echo  ██║  ██║██╔════╝██╔══██╗██║  ╚══██╔══╝██║  ██║    ██╔══██╗██║
echo  ███████║█████╗  ███████║██║     ██║   ███████║    ███████║██║
echo  ██╔══██║██╔══╝  ██╔══██║██║     ██║   ██╔══██║    ██╔══██║██║
echo  ██║  ██║███████╗██║  ██║███████╗██║   ██║  ██║    ██║  ██║██║
echo  ╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝╚══════╝╚═╝   ╚═╝  ╚═╝    ╚═╝  ╚═╝╚═╝
echo.
echo                    🚀 ULTIMATE FIX - GUARANTEED TO WORK 🚀
echo ================================================================================
echo This will fix ALL issues and get your Health AI Assistant working for real users
echo ================================================================================
echo.

REM Step 1: Check location
echo 📍 STEP 1: Checking location...
echo Current directory: %CD%
if not exist "backend" (
    echo.
    echo ❌ ERROR: backend folder not found
    echo Please navigate to your health-ai project folder first
    echo.
    echo Example: cd "C:\Users\<USER>\Desktop\health-ai"
    echo.
    pause
    exit /b 1
)
echo ✅ Found backend folder

if not exist "frontend" (
    echo ❌ ERROR: frontend folder not found
    pause
    exit /b 1
)
echo ✅ Found frontend folder

REM Step 2: Install Python packages
echo.
echo 📦 STEP 2: Installing Python packages...
echo This may take 2-3 minutes...

python -m pip install --upgrade pip
python -m pip install fastapi==0.104.1
python -m pip install uvicorn[standard]==0.24.0
python -m pip install python-multipart==0.0.6
python -m pip install pydantic==2.5.2

echo ✅ Python packages installed

REM Step 3: Test Python imports
echo.
echo 🧪 STEP 3: Testing Python setup...
python -c "
import sys
print(f'Python version: {sys.version}')

try:
    import fastapi
    print('✅ FastAPI: OK')
except:
    print('❌ FastAPI: FAILED')

try:
    import uvicorn
    print('✅ Uvicorn: OK')
except:
    print('❌ Uvicorn: FAILED')

try:
    import pydantic
    print('✅ Pydantic: OK')
except:
    print('❌ Pydantic: FAILED')
"

REM Step 4: Start backend
echo.
echo 🚀 STEP 4: Starting backend server...
echo ================================================================================
echo.
echo 📍 Backend will be available at: http://localhost:8000
echo 📚 API Documentation: http://localhost:8000/docs
echo.
echo ✅ This backend supports REAL USER REGISTRATION
echo ✅ Data is stored in memory (will upgrade to MongoDB later)
echo ✅ Multiple users can register and login
echo.
echo 🔥 LEAVE THIS WINDOW OPEN - Backend is running here
echo.
echo Starting in 3 seconds...
timeout /t 3 /nobreak >nul

cd backend
python simple_working_backend.py

echo.
echo 🛑 Backend stopped
pause
