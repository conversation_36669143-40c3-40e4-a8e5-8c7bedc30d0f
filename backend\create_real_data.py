"""
Create real sample data in MongoDB
"""

import asyncio
import os
from datetime import datetime, timedelta
import random
from dotenv import load_dotenv
from motor.motor_asyncio import AsyncIOMotorClient
from passlib.context import CryptContext
import uuid

# Load environment variables
load_dotenv()

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

async def create_sample_data():
    """Create sample users and health data"""
    
    mongodb_url = os.getenv("MONGODB_URL")
    if not mongodb_url:
        print("❌ MONGODB_URL not found in .env file")
        return
    
    print("🔗 Connecting to MongoDB...")
    client = AsyncIOMotorClient(mongodb_url)
    db = client.health_ai
    
    try:
        # Test connection
        await client.admin.command('ping')
        print("✅ Connected to MongoDB successfully!")
        
        # Create sample users
        users_collection = db.users
        
        # Check if demo user already exists
        existing_user = await users_collection.find_one({"email": "<EMAIL>"})
        if existing_user:
            print("✅ Demo user already exists")
        else:
            # Create demo user
            demo_user = {
                "_id": "demo-user-id",
                "email": "<EMAIL>",
                "hashed_password": get_password_hash("demo123456"),
                "first_name": "Demo",
                "last_name": "User",
                "client_id": str(uuid.uuid4()),
                "is_active": True,
                "is_verified": True,
                "profile": {
                    "age": 30,
                    "gender": "female",
                    "height_cm": 165,
                    "weight_kg": 65,
                    "activity_level": "moderately_active",
                    "health_goals": ["general_fitness", "better_sleep"],
                    "medical_conditions": [],
                    "medications": [],
                    "allergies": ["peanuts"],
                    "emergency_contact": ""
                },
                "privacy_settings": {
                    "share_data_for_research": False,
                    "allow_data_analytics": True,
                    "federated_learning_participation": True,
                    "data_retention_days": 365
                },
                "created_at": datetime.utcnow(),
                "last_login": datetime.utcnow()
            }
            
            await users_collection.insert_one(demo_user)
            print("✅ Created demo user: <EMAIL>")
        
        # Create sample health data
        health_data_collection = db.health_data
        
        # Check if health data already exists
        existing_data = await health_data_collection.find_one({"user_id": "demo-user-id"})
        if existing_data:
            print("✅ Health data already exists")
        else:
            print("📊 Creating sample health data...")
            
            # Create 30 days of health data
            health_data_entries = []
            for days_ago in range(30):
                date = datetime.utcnow() - timedelta(days=days_ago)
                
                # Generate realistic health data
                steps = random.randint(6000, 12000)
                sleep_hours = round(random.uniform(6.5, 8.5), 1)
                heart_rate = random.randint(65, 85)
                stress_level = random.randint(2, 7)
                weight = round(65 + random.uniform(-1, 1), 1)
                
                entry = {
                    "_id": f"health-data-{days_ago}",
                    "user_id": "demo-user-id",
                    "date": date,
                    "data_source": "simulated",
                    "vital_signs": {
                        "heart_rate_bpm": heart_rate,
                        "blood_pressure_systolic": random.randint(110, 130),
                        "blood_pressure_diastolic": random.randint(70, 85),
                        "body_temperature_celsius": round(36.5 + random.uniform(-0.3, 0.3), 1),
                        "oxygen_saturation": round(random.uniform(96, 100), 1)
                    },
                    "activity": {
                        "steps": steps,
                        "distance_km": round(steps * 0.0008, 2),
                        "calories_burned": int(steps * 0.04),
                        "active_minutes": random.randint(30, 90),
                        "floors_climbed": random.randint(5, 15)
                    },
                    "sleep": {
                        "total_sleep_hours": sleep_hours,
                        "deep_sleep_hours": round(sleep_hours * 0.2, 1),
                        "light_sleep_hours": round(sleep_hours * 0.5, 1),
                        "rem_sleep_hours": round(sleep_hours * 0.3, 1),
                        "sleep_efficiency": round(random.uniform(80, 95), 1),
                        "bedtime": date.replace(hour=22, minute=random.randint(0, 60)),
                        "wake_time": date.replace(hour=7, minute=random.randint(0, 60))
                    },
                    "nutrition": {
                        "calories_consumed": random.randint(1800, 2400),
                        "protein_grams": round(random.uniform(80, 120), 1),
                        "carbs_grams": round(random.uniform(200, 300), 1),
                        "fat_grams": round(random.uniform(60, 100), 1),
                        "fiber_grams": round(random.uniform(20, 35), 1),
                        "water_liters": round(random.uniform(2, 4), 1)
                    },
                    "mental_health": {
                        "stress_level": stress_level,
                        "mood_score": random.randint(6, 9),
                        "anxiety_level": random.randint(1, 5),
                        "meditation_minutes": random.randint(0, 20)
                    },
                    "weight_kg": weight,
                    "body_fat_percentage": round(random.uniform(18, 25), 1),
                    "muscle_mass_kg": round(random.uniform(25, 30), 1)
                }
                
                health_data_entries.append(entry)
            
            # Insert all health data
            await health_data_collection.insert_many(health_data_entries)
            print(f"✅ Created {len(health_data_entries)} health data entries")
        
        # Create sample chat session
        chat_sessions_collection = db.chat_sessions
        existing_session = await chat_sessions_collection.find_one({"user_id": "demo-user-id"})
        if not existing_session:
            chat_session = {
                "_id": "demo-chat-session",
                "user_id": "demo-user-id",
                "title": "Health Chat",
                "description": "General health discussion",
                "is_active": True,
                "message_count": 2,
                "health_focus": ["general"],
                "user_goals": ["general_fitness"],
                "started_at": datetime.utcnow(),
                "last_activity": datetime.utcnow(),
                "ended_at": None
            }
            await chat_sessions_collection.insert_one(chat_session)
            print("✅ Created sample chat session")
        
        print("\n🎉 Sample data creation complete!")
        print("=" * 50)
        print("You can now login with:")
        print("Email: <EMAIL>")
        print("Password: demo123456")
        print("\nThe app will now show real data from MongoDB!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    asyncio.run(create_sample_data())
