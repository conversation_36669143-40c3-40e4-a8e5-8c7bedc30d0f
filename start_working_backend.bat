@echo off
echo 🏥 Health AI - Working Backend with Registration
echo ===============================================

cd backend

echo 📦 Installing required packages...
python -m pip install fastapi uvicorn motor python-dotenv passlib[bcrypt] PyJWT python-multipart

echo 🧪 Testing MongoDB connection...
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
mongodb_url = os.getenv('MONGODB_URL')
if mongodb_url:
    print('✅ MongoDB URL found')
else:
    print('❌ MongoDB URL not found - check .env file')
"

echo.
echo 🚀 Starting working backend...
echo This version supports real user registration!
echo.
echo 📍 Backend: http://localhost:8000
echo 📚 API Docs: http://localhost:8000/docs
echo.

python working_main.py

pause
