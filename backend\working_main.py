"""
Working FastAPI backend with proper user registration
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2PasswordRe<PERSON>Form
from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime, timedelta
import secrets
import uuid
import os
from dotenv import load_dotenv
from motor.motor_asyncio import AsyncIOMotorClient
from passlib.context import CryptContext
import jwt

# Load environment variables
load_dotenv()

# Configuration
MONGODB_URL = os.getenv("MONGODB_URL")
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-this")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# MongoDB client
client = None
db = None

# FastAPI app
app = FastAPI(
    title="Health AI Assistant",
    version="1.0.0",
    description="AI-Powered Healthcare Assistant with Real Database"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class UserCreate(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: str

class UserResponse(BaseModel):
    id: str
    email: str
    first_name: str
    last_name: str
    is_active: bool
    created_at: datetime

class Token(BaseModel):
    access_token: str
    token_type: str
    user: UserResponse

# Helper functions
def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

# Database connection
@app.on_event("startup")
async def startup_db_client():
    global client, db
    if MONGODB_URL:
        client = AsyncIOMotorClient(MONGODB_URL)
        db = client.health_ai
        print("✅ Connected to MongoDB")
    else:
        print("❌ MongoDB URL not configured")

@app.on_event("shutdown")
async def shutdown_db_client():
    if client:
        client.close()

# Routes
@app.get("/")
async def root():
    return {
        "message": "Health AI Assistant API - Working Version",
        "version": "1.0.0",
        "status": "running",
        "database": "connected" if db else "not connected"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "database": "connected" if db else "not connected",
        "timestamp": datetime.utcnow().isoformat()
    }

@app.post("/api/v1/auth/register")
async def register(user_data: UserCreate):
    """Register a new user"""
    if not db:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database not connected"
        )
    
    try:
        # Check if user already exists
        existing_user = await db.users.find_one({"email": user_data.email})
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Create new user
        user_id = str(uuid.uuid4())
        hashed_password = get_password_hash(user_data.password)
        
        user_doc = {
            "_id": user_id,
            "email": user_data.email,
            "hashed_password": hashed_password,
            "first_name": user_data.first_name,
            "last_name": user_data.last_name,
            "is_active": True,
            "is_verified": True,  # Skip email verification for now
            "created_at": datetime.utcnow(),
            "profile": {
                "age": None,
                "gender": None,
                "height_cm": None,
                "weight_kg": None,
                "activity_level": "moderately_active",
                "health_goals": [],
                "medical_conditions": [],
                "medications": [],
                "allergies": []
            },
            "privacy_settings": {
                "federated_learning_participation": True,
                "allow_data_analytics": True,
                "share_data_for_research": False,
                "data_retention_days": 365
            }
        }
        
        # Insert user into database
        await db.users.insert_one(user_doc)
        
        print(f"✅ User registered: {user_data.email}")
        
        return {
            "message": "Registration successful",
            "user": UserResponse(
                id=user_id,
                email=user_data.email,
                first_name=user_data.first_name,
                last_name=user_data.last_name,
                is_active=True,
                created_at=user_doc["created_at"]
            )
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )

@app.post("/api/v1/auth/login")
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    """Login user"""
    if not db:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database not connected"
        )
    
    try:
        # Find user
        user = await db.users.find_one({"email": form_data.username})
        if not user or not verify_password(form_data.password, user["hashed_password"]):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if not user.get("is_active", True):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive user"
            )
        
        # Create access token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user["email"]}, expires_delta=access_token_expires
        )
        
        # Update last login
        await db.users.update_one(
            {"_id": user["_id"]},
            {"$set": {"last_login": datetime.utcnow()}}
        )
        
        print(f"✅ User logged in: {user['email']}")
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            user=UserResponse(
                id=user["_id"],
                email=user["email"],
                first_name=user["first_name"],
                last_name=user["last_name"],
                is_active=user.get("is_active", True),
                created_at=user["created_at"]
            )
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed: {str(e)}"
        )

@app.get("/api/v1/dashboard/metrics")
async def get_dashboard_metrics():
    """Get dashboard metrics"""
    return {
        "health_score": 75,
        "recent_activity": {
            "steps": 8500,
            "calories_burned": 320,
            "active_minutes": 45,
            "distance_km": 6.8
        },
        "sleep_quality": 80,
        "stress_level": 4,
        "recommendations_count": 5,
        "goals_progress": [
            {
                "goal": "Daily Steps",
                "progress": 8500,
                "target": 10000,
                "unit": "steps",
                "status": "on_track"
            }
        ],
        "health_trends": {
            "steps": {"trend": "increasing"},
            "sleep": {"trend": "stable"},
            "stress": {"trend": "decreasing"}
        },
        "last_updated": datetime.utcnow().isoformat()
    }

@app.post("/api/v1/chat/message")
async def send_chat_message(content: str = Form(...), category: str = Form("general")):
    """Send a message to AI assistant"""
    
    responses = {
        "nutrition": "Great question about nutrition! Based on your profile, I recommend focusing on a balanced diet with plenty of vegetables, lean proteins, and whole grains.",
        "fitness": "For your fitness goals, I suggest starting with 30 minutes of moderate exercise daily. Consistency is key!",
        "sleep": "Good sleep is crucial for health! Aim for 7-9 hours nightly with a consistent bedtime routine.",
        "mental_health": "Mental wellness is important. Consider stress-reduction techniques like meditation or deep breathing.",
        "general": "I'm here to help with your health journey! What specific area would you like to focus on?"
    }
    
    response_content = responses.get(category, responses["general"])
    
    return {
        "id": f"msg-{secrets.token_hex(8)}",
        "session_id": "session-1",
        "message_type": "assistant",
        "content": response_content,
        "category": category,
        "recommendations": [
            "Track your daily water intake",
            "Aim for 10,000 steps per day",
            "Maintain a consistent sleep schedule"
        ],
        "confidence_score": 0.85,
        "timestamp": datetime.utcnow().isoformat()
    }

if __name__ == "__main__":
    import uvicorn
    print("🏥 Starting Health AI Assistant - Working Version")
    print("📍 Backend: http://localhost:8000")
    print("📚 API Docs: http://localhost:8000/docs")
    print("🔧 This version handles real user registration!")
    uvicorn.run(app, host="0.0.0.0", port=8000)
