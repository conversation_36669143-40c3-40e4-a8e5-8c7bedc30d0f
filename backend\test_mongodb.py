"""
Test MongoDB connection
"""

import asyncio
import os
from motor.motor_asyncio import AsyncIOMotorClient
from dotenv import load_dotenv

async def test_mongodb():
    # Load environment variables
    load_dotenv()
    
    mongodb_url = os.getenv("MONGODB_URL")
    print(f"Testing connection to: {mongodb_url[:50]}...")
    
    try:
        # Create client
        client = AsyncIOMotorClient(mongodb_url)
        
        # Test connection
        await client.admin.command('ping')
        print("✅ MongoDB connection successful!")
        
        # Test database access
        db = client.health_ai
        collections = await db.list_collection_names()
        print(f"✅ Database access successful! Collections: {collections}")
        
        # Close connection
        client.close()
        
    except Exception as e:
        print(f"❌ MongoDB connection failed: {e}")
        print("Please check:")
        print("1. MongoDB Atlas cluster is running")
        print("2. IP address is whitelisted")
        print("3. Username and password are correct")
        print("4. Database name is specified in connection string")

if __name__ == "__main__":
    asyncio.run(test_mongodb())
